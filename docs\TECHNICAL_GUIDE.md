# 🔬 技术指南 / Technical Guide

## ESP32-C3 特性 / ESP32-C3 Features

### 硬件规格 / Hardware Specifications
- **CPU**: RISC-V 32位单核 160MHz / RISC-V 32-bit single-core 160MHz
- **内存 / Memory**: 400KB SRAM, 384KB ROM
- **Flash**: 4MB (可扩展 / expandable)
- **WiFi**: 802.11 b/g/n (2.4GHz)
- **蓝牙 / Bluetooth**: Bluetooth 5 (LE)
- **GPIO**: 22个可用GPIO / 22 available GPIO pins
- **ADC**: 2个12位ADC / 2x 12-bit ADC
- **UART**: 2个硬件UART / 2x hardware UART
- **USB**: 原生USB 2.0 FS / Native USB 2.0 FS

### 电源管理 / Power Management
- **工作电压 / Operating voltage**: 3.0V - 3.6V
- **深度睡眠电流 / Deep sleep current**: ~10µA
- **活跃电流 / Active current**: ~80mA (WiFi传输 / WiFi transmission)
- **RTC电流 / RTC current**: ~5µA

## 传感器接口 / Sensor Interface

### TDS传感器通信协议 / TDS Sensor Communication Protocol

#### 命令格式 / Command Format
```
发送 / Send: 0x01 0x03 0x00 0x00 0x00 0x02 0xC4 0x0B
响应 / Response: 0x01 0x03 0x04 [TDS_H] [TDS_L] [TEMP_H] [TEMP_L] [CRC_H] [CRC_L]
```

#### 数据解析 / Data Parsing
```cpp
// TDS值计算 / TDS value calculation
uint16_t tds_raw = (response[3] << 8) | response[4];
float tds_ppm = tds_raw / 10.0;  // 转换为ppm / Convert to ppm

// 温度值计算 / Temperature value calculation  
uint16_t temp_raw = (response[5] << 8) | response[6];
float temperature = temp_raw / 10.0;  // 转换为摄氏度 / Convert to Celsius
```

### 串口配置 / Serial Configuration
- **波特率 / Baud rate**: 9600
- **数据位 / Data bits**: 8
- **停止位 / Stop bits**: 1
- **校验位 / Parity**: None
- **流控制 / Flow control**: None

## 网络架构 / Network Architecture

### WiFi连接流程 / WiFi Connection Flow
```mermaid
graph TD
    A[系统启动 / System Start] --> B[检查WiFi配置 / Check WiFi Config]
    B --> C{配置存在? / Config Exists?}
    C -->|是 / Yes| D[连接WiFi / Connect WiFi]
    C -->|否 / No| E[启动BluFi配网 / Start BluFi Provisioning]
    E --> F[等待配网 / Wait for Provisioning]
    F --> D
    D --> G{连接成功? / Connected?}
    G -->|是 / Yes| H[连接MQTT / Connect MQTT]
    G -->|否 / No| I[重试连接 / Retry Connection]
    I --> D
    H --> J[开始数据采集 / Start Data Collection]
```

### MQTT消息流 / MQTT Message Flow
```mermaid
sequenceDiagram
    participant D as Device
    participant B as MQTT Broker
    participant S as Server/App
    
    D->>B: 连接 / Connect
    B->>D: 连接确认 / CONNACK
    D->>B: 发布状态 / Publish Status
    D->>B: 发布传感器数据 / Publish Sensor Data
    S->>B: 订阅主题 / Subscribe Topics
    B->>S: 转发数据 / Forward Data
    D->>B: 心跳消息 / Heartbeat
```

## 数据存储 / Data Storage

### NVS存储结构 / NVS Storage Structure
```cpp
// 存储键值 / Storage Keys
namespace: "fishtank"
keys:
  - "last_tds"      // 上次TDS值 / Last TDS value
  - "last_temp"     // 上次温度值 / Last temperature value
  - "last_report"   // 上次上报时间 / Last report time
  - "boot_count"    // 启动次数 / Boot count
  - "wifi_ssid"     // WiFi SSID
  - "wifi_pass"     // WiFi密码 / WiFi password
  - "device_id"     // 设备ID / Device ID
```

### 数据持久化策略 / Data Persistence Strategy
1. **关键数据 / Critical data**: 立即写入NVS / Write to NVS immediately
2. **统计数据 / Statistics**: 批量写入 / Batch write
3. **临时数据 / Temporary data**: 仅内存存储 / Memory only
4. **配置数据 / Configuration**: 启动时读取 / Read at startup

## 电源优化 / Power Optimization

### 深度睡眠配置 / Deep Sleep Configuration
```cpp
// 配置唤醒源 / Configure wake-up sources
esp_sleep_enable_timer_wakeup(sleep_duration * 1000000ULL);

// 禁用不必要的外设 / Disable unnecessary peripherals
esp_wifi_stop();
esp_bt_controller_disable();

// 进入深度睡眠 / Enter deep sleep
esp_deep_sleep_start();
```

### 功耗优化技巧 / Power Optimization Tips
1. **最小化WiFi连接时间 / Minimize WiFi connection time**
2. **使用最低可能的CPU频率 / Use lowest possible CPU frequency**
3. **禁用未使用的外设 / Disable unused peripherals**
4. **优化代码执行效率 / Optimize code execution efficiency**
5. **使用RTC内存存储临时数据 / Use RTC memory for temporary data**

## 错误处理 / Error Handling

### 错误类型 / Error Types
```cpp
enum ErrorType {
    ERROR_SENSOR_TIMEOUT,    // 传感器超时 / Sensor timeout
    ERROR_SENSOR_CRC,        // CRC校验失败 / CRC check failed
    ERROR_WIFI_CONNECT,      // WiFi连接失败 / WiFi connection failed
    ERROR_MQTT_CONNECT,      // MQTT连接失败 / MQTT connection failed
    ERROR_LOW_BATTERY,       // 电池电量低 / Low battery
    ERROR_MEMORY_FULL        // 内存不足 / Memory full
};
```

### 错误恢复策略 / Error Recovery Strategy
1. **传感器错误 / Sensor errors**: 重试3次，然后跳过本次读取 / Retry 3 times, then skip reading
2. **网络错误 / Network errors**: 指数退避重试 / Exponential backoff retry
3. **内存错误 / Memory errors**: 清理缓存，重启系统 / Clear cache, restart system
4. **电池低电量 / Low battery**: 降低采样频率 / Reduce sampling frequency

## 性能监控 / Performance Monitoring

### 关键指标 / Key Metrics
- **堆内存使用 / Heap memory usage**: `ESP.getFreeHeap()`
- **WiFi信号强度 / WiFi signal strength**: `WiFi.RSSI()`
- **电池电压 / Battery voltage**: ADC读取 / ADC reading
- **运行时间 / Uptime**: `millis()`
- **启动次数 / Boot count**: NVS存储 / NVS stored

### 监控代码示例 / Monitoring Code Example
```cpp
void printSystemStats() {
    DEBUG_PRINT("=== 系统状态 / System Status ===");
    DEBUG_PRINT("自由堆内存 / Free Heap: " + String(ESP.getFreeHeap()) + " bytes");
    DEBUG_PRINT("WiFi信号 / WiFi RSSI: " + String(WiFi.RSSI()) + " dBm");
    DEBUG_PRINT("运行时间 / Uptime: " + String(millis() / 1000) + " seconds");
    DEBUG_PRINT("启动次数 / Boot Count: " + String(bootCount));
}
```

## 安全考虑 / Security Considerations

### 网络安全 / Network Security
1. **使用TLS加密 / Use TLS encryption**: 对于生产环境 / For production environments
2. **强密码策略 / Strong password policy**: WiFi和MQTT密码 / WiFi and MQTT passwords
3. **定期更新固件 / Regular firmware updates**: 修复安全漏洞 / Fix security vulnerabilities
4. **限制网络访问 / Limit network access**: 仅必要的端口和协议 / Only necessary ports and protocols

### 数据保护 / Data Protection
1. **敏感数据加密 / Encrypt sensitive data**: 存储在NVS中的密码 / Passwords stored in NVS
2. **数据完整性检查 / Data integrity checks**: CRC校验 / CRC verification
3. **访问控制 / Access control**: MQTT主题权限 / MQTT topic permissions
4. **审计日志 / Audit logging**: 记录关键操作 / Log critical operations
