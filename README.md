# 🐟 Fish Tank Monitoring System

一个基于ESP32-C3的智能鱼缸监控系统，具备TDS（总溶解固体）和温度监控功能，支持MQTT远程监控和USB串口调试。

A smart IoT fish tank monitoring system built with ESP32-C3, featuring TDS (Total Dissolved Solids) and temperature monitoring with MQTT connectivity and USB serial debugging.

## ✨ 主要功能 / Features

- **🌊 TDS监控 / TDS Monitoring**: 实时水质检测，监控总溶解固体含量 / Real-time water quality measurement
- **🌡️ 温度监控 / Temperature Monitoring**: 连续水温跟踪，确保鱼类生存环境 / Continuous water temperature tracking
- **📡 MQTT连接 / MQTT Connectivity**: 远程数据传输和实时监控 / Remote data transmission and monitoring
- **🔋 电源管理 / Power Management**: 深度睡眠模式，优化电池使用效率 / Deep sleep mode for battery efficiency
- **📶 WiFi配网 / WiFi Provisioning**: 通过BluFi轻松设置网络连接 / Easy network setup via BluFi
- **💾 数据持久化 / Data Persistence**: 使用NVS本地存储 / Local storage with NVS (Non-Volatile Storage)
- **🧠 智能报告 / Intelligent Reporting**: 基于阈值和周期性的智能数据上报 / Threshold-based and periodic reporting
- **🔧 USB调试 / USB Debugging**: 通过USB串口获取调试信息 / Get debug info via USB serial
- **📊 数据统计 / Data Statistics**: 详细的系统统计和日志记录 / Comprehensive logging and system statistics

## 🛠️ 硬件要求 / Hardware Requirements

- **主控 / MCU**: ESP32-C3开发板 / ESP32-C3 Development Board
- **传感器 / Sensor**: 带UART接口的TDS传感器 / TDS sensor with UART interface
- **温度传感器 / Temperature Sensor**: 集成在TDS传感器中 / Integrated with TDS sensor
- **电源 / Power**: USB供电或电池供电 / USB or battery power
- **网络 / Network**: WiFi网络接入 / WiFi network access
- **服务器 / Server**: MQTT代理服务器 / MQTT broker

## 🔌 硬件连接 / Wiring

| ESP32-C3引脚 / Pin | TDS传感器 / TDS Sensor | 说明 / Description |
|-------------------|----------------------|-------------------|
| GPIO4             | TX                   | 传感器发送 / Sensor TX |
| GPIO5             | RX                   | 传感器接收 / Sensor RX |
| 3.3V              | VCC                  | 电源正极 / Power + |
| GND               | GND                  | 电源负极 / Power - |

> **注意 / Note**: ESP32-C3使用USB-C接口进行供电和调试 / ESP32-C3 uses USB-C for power and debugging

## 🚀 软件设置 / Software Setup

### 1. 环境准备 / Environment Setup

```bash
# 安装PlatformIO / Install PlatformIO
pip install platformio

# 克隆项目 / Clone project
git clone <repository-url>
cd fish-tank-monitoring
```

### 2. 配置WiFi和MQTT / Configure WiFi and MQTT

编辑 `src/Config.h` 文件 / Edit `src/Config.h` file:

```cpp
// WiFi配置 / WiFi Configuration (可选，支持BluFi配网 / Optional, supports BluFi provisioning)
#define WIFI_SSID "YOUR_WIFI_SSID"
#define WIFI_PASSWORD "YOUR_WIFI_PASSWORD"

// MQTT配置 / MQTT Configuration
#define MQTT_SERVER "your-mqtt-broker.com"
#define MQTT_PORT 1883
#define MQTT_USER "your_mqtt_user"      // 留空表示无认证 / Leave empty if no auth
#define MQTT_PASSWORD "your_mqtt_pass"  // 留空表示无认证 / Leave empty if no auth

// 传感器配置 / Sensor Configuration
#define TDS_THRESHOLD_PPM 50           // TDS阈值 / TDS threshold (ppm)
#define TEMP_THRESHOLD_C 2.0           // 温度阈值 / Temperature threshold (°C)
#define REPORT_INTERVAL_SECONDS 3600   // 上报间隔 / Report interval (1 hour)
```

### 3. 自动安装依赖 / Auto Install Dependencies

PlatformIO会自动安装所需库 / PlatformIO will automatically install required libraries:
- **ArduinoJson @ 7.4.2** - JSON数据处理 / JSON data processing
- **PubSubClient @ 2.8.0** - MQTT通信 / MQTT communication
- **Preferences @ 2.0.0** - 数据存储 / Data storage
- **WiFi Libraries** - WiFi连接和配网 / WiFi connection and provisioning

### 4. 编译上传 / Build and Upload

```bash
# 编译项目 / Build project
platformio run

# 上传到设备 / Upload to device
platformio run --target upload

# 监控串口输出 / Monitor serial output
platformio device monitor
```

### 5. USB调试 / USB Debugging

ESP32-C3支持USB串口调试 / ESP32-C3 supports USB serial debugging:

1. 连接USB-C线缆 / Connect USB-C cable
2. 设备会出现为串口设备 / Device will appear as serial port
3. 使用串口监控工具查看调试信息 / Use serial monitor to view debug info

```bash
# PlatformIO串口监控 / PlatformIO serial monitor
pio device monitor --baud 115200

# 或使用其他串口工具 / Or use other serial tools
# Windows: PuTTY, Tera Term
# macOS/Linux: screen, minicom
```

## 🏗️ 项目结构 / Project Structure

```
fish-tank-monitoring/
├── src/
│   ├── main.cpp                 # 主程序入口 / Main program entry
│   ├── Config.h                 # 配置文件 / Configuration file
│   ├── SensorDataManager.h/cpp  # 传感器数据管理 / Sensor data management
│   ├── PowerManager.h/cpp       # 电源管理 / Power management
│   ├── ConnectivityManager.h/cpp # 网络连接管理 / Network connectivity
│   └── ReportingLogic.h/cpp     # 数据上报逻辑 / Data reporting logic
├── lib/
│   └── TDS_Sensor_UART/         # TDS传感器库 / TDS sensor library
│       ├── TDS_Sensor_UART.h
│       └── TDS_Sensor_UART.cpp
├── platformio.ini               # PlatformIO配置 / PlatformIO config
├── README.md                    # 项目文档 / Project documentation
└── docs/                        # 详细文档 / Detailed documentation
```

## ⚙️ 系统行为 / System Behavior

### 数据采集 / Data Collection
- 每5-10分钟唤醒一次（随机间隔）/ Wakes up every 5-10 minutes (random interval)
- 从传感器读取TDS和温度 / Reads TDS and temperature from sensor
- 将读数存储在NVS中 / Stores readings in NVS (non-volatile storage)

### 上报逻辑 / Reporting Logic
满足以下任一条件时通过MQTT上报数据 / Data is reported via MQTT when any condition is met:

1. **TDS变化 / TDS Change**: 当前TDS与上次上报值相差超过阈值 / Current TDS differs from last reported by threshold
2. **温度变化 / Temperature Change**: 当前温度与上次上报值相差超过阈值 / Current temperature differs from last reported by threshold
3. **定时上报 / Periodic Report**: 每小时定时上报 / Hourly periodic reports
4. **错误报告 / Error Report**: 传感器错误或系统异常 / Sensor errors or system exceptions

### 电源管理 / Power Management
- 读数间隔期间深度睡眠 / Deep sleep between readings for maximum battery life
- 使用NVS在睡眠周期间保持状态 / Maintains state across sleep cycles using NVS
- 典型功耗 / Typical power consumption: ~10µA in deep sleep

## 📡 MQTT主题 / MQTT Topics

系统发布到以下主题 / The system publishes to the following topics:

| 主题 / Topic | 用途 / Purpose | 说明 / Description |
|-------------|---------------|-------------------|
| `fishtank/{device_id}/sensor` | 传感器数据 / Sensor data | TDS和温度数据 / TDS and temperature data |
| `fishtank/{device_id}/status` | 设备状态 / Device status | 系统状态信息 / System status info |
| `fishtank/{device_id}/error` | 错误报告 / Error reports | 系统错误和异常 / System errors and exceptions |
| `fishtank/{device_id}/heartbeat` | 心跳消息 / Heartbeat messages | 设备在线状态 / Device online status |

## 📊 数据格式 / Data Format

### 传感器数据 / Sensor Data Format

```json
{
  "device_id": "fishtank_001",
  "timestamp": 1640995200,
  "tds_ppm": 245,
  "temperature_c": 24.5,
  "battery_voltage": 3.8,
  "wifi_rssi": -45,
  "report_reason": "tds_threshold",
  "uptime_seconds": 3600,
  "free_heap": 180000,
  "boot_count": 15
}
```

### 状态数据 / Status Data Format

```json
{
  "device_id": "fishtank_001",
  "timestamp": 1640995200,
  "status": "online",
  "firmware_version": "1.0.0",
  "wifi_rssi": -45,
  "free_heap": 180000,
  "boot_count": 15,
  "uptime_seconds": 3600
}
```

### 错误报告 / Error Report Format

```json
{
  "device_id": "fishtank_001",
  "timestamp": 1640995200,
  "error_type": "sensor_timeout",
  "error_message": "TDS sensor did not respond within timeout period",
  "battery_voltage": 3.7,
  "wifi_rssi": -50,
  "free_heap": 175000
}
```

## ⚙️ 配置选项 / Configuration Options

`src/Config.h` 中的关键配置参数 / Key configuration parameters in `src/Config.h`:

### 网络配置 / Network Configuration
| 参数 / Parameter | 默认值 / Default | 说明 / Description |
|-----------------|-----------------|-------------------|
| `WIFI_SSID` | "your_wifi" | WiFi网络名称 / WiFi network name |
| `WIFI_PASSWORD` | "your_password" | WiFi密码 / WiFi password |
| `MQTT_SERVER` | "broker.hivemq.com" | MQTT代理服务器 / MQTT broker server |
| `MQTT_PORT` | 1883 | MQTT端口 / MQTT port |
| `MQTT_USER` | "" | MQTT用户名 / MQTT username |
| `MQTT_PASSWORD` | "" | MQTT密码 / MQTT password |

### 传感器配置 / Sensor Configuration
| 参数 / Parameter | 默认值 / Default | 说明 / Description |
|-----------------|-----------------|-------------------|
| `TDS_THRESHOLD_PPM` | 50 | TDS变化阈值(ppm) / TDS change threshold |
| `TEMP_THRESHOLD_C` | 2.0 | 温度变化阈值(°C) / Temperature change threshold |
| `SENSOR_TIMEOUT_MS` | 5000 | 传感器超时时间 / Sensor timeout |
| `SENSOR_RETRY_COUNT` | 3 | 传感器重试次数 / Sensor retry count |

### 电源管理 / Power Management
| 参数 / Parameter | 默认值 / Default | 说明 / Description |
|-----------------|-----------------|-------------------|
| `SLEEP_MIN_SECONDS` | 300 | 最小睡眠时间(秒) / Minimum sleep time |
| `SLEEP_MAX_SECONDS` | 600 | 最大睡眠时间(秒) / Maximum sleep time |
| `REPORT_INTERVAL_SECONDS` | 3600 | 定时上报间隔(秒) / Periodic report interval |
| `LOW_BATTERY_THRESHOLD` | 3.2 | 低电量阈值(V) / Low battery threshold |

### 调试配置 / Debug Configuration
| 参数 / Parameter | 默认值 / Default | 说明 / Description |
|-----------------|-----------------|-------------------|
| `ENABLE_SERIAL_DEBUG` | 1 | 启用串口调试 / Enable serial debugging |
| `DEBUG_LEVEL` | 2 | 调试级别(0-3) / Debug level |
| `WIFI_TIMEOUT_MS` | 30000 | WiFi连接超时 / WiFi connection timeout |
| `MQTT_TIMEOUT_MS` | 10000 | MQTT连接超时 / MQTT connection timeout |

## 🔋 功耗分析 / Power Consumption

系统设计为超低功耗运行 / The system is designed for ultra-low power operation:

### ESP32-C3功耗特性 / ESP32-C3 Power Characteristics
- **活跃时间 / Active time**: ~30-60秒每周期 / ~30-60 seconds per cycle (sensor reading + reporting)
- **睡眠时间 / Sleep time**: 5-10分钟（可配置）/ 5-10 minutes (configurable)
- **深度睡眠电流 / Deep sleep current**: ~10µA (ESP32-C3典型值 / ESP32-C3 typical)
- **活跃电流 / Active current**: ~80mA (WiFi传输时 / during WiFi transmission)

### 电池寿命估算 / Battery Life Estimation
| 电池类型 / Battery Type | 容量 / Capacity | 预估寿命 / Estimated Life |
|----------------------|----------------|-------------------------|
| 18650锂电池 / 18650 Li-ion | 3000mAh | 6-12个月 / 6-12 months |
| CR123A锂电池 / CR123A Li | 1500mAh | 3-6个月 / 3-6 months |
| AA碱性电池 / AA Alkaline | 2500mAh | 4-8个月 / 4-8 months |

> **注意 / Note**: 实际电池寿命取决于上报频率、WiFi信号强度和环境温度 / Actual battery life depends on reporting frequency, WiFi signal strength, and ambient temperature

## 🔧 故障排除 / Troubleshooting

### 常见问题 / Common Issues

#### 1. 传感器无响应 / Sensor not responding
- **检查接线 / Check wiring**: 确认GPIO4/5连接正确 / Verify GPIO4/5 connections
- **检查电源 / Verify power**: 确认3.3V供电稳定 / Ensure stable 3.3V power
- **检查串口设置 / Check serial settings**: 波特率9600，8N1 / Baud rate 9600, 8N1
- **传感器测试 / Sensor test**: 使用串口工具直接测试传感器 / Test sensor directly with serial tool

#### 2. WiFi连接失败 / WiFi connection fails
- **检查配置 / Verify config**: 确认Config.h中的SSID和密码 / Check SSID and password in Config.h
- **信号强度 / Signal strength**: 确保WiFi信号足够强 / Ensure sufficient WiFi signal
- **频段支持 / Frequency band**: 确保使用2.4GHz网络 / Ensure 2.4GHz network (ESP32-C3不支持5GHz / doesn't support 5GHz)
- **网络类型 / Network type**: 避免企业级WiFi认证 / Avoid enterprise WiFi authentication

#### 3. MQTT连接失败 / MQTT connection fails
- **代理地址 / Broker address**: 验证MQTT代理地址和端口 / Verify MQTT broker address and port
- **认证信息 / Authentication**: 检查用户名和密码 / Check username and password
- **网络访问 / Network access**: 确保代理可从网络访问 / Ensure broker is accessible from network
- **防火墙 / Firewall**: 检查防火墙设置 / Check firewall settings

#### 4. 频繁重启 / Frequent reboots
- **电源稳定性 / Power stability**: 检查电源供应稳定性 / Check power supply stability
- **串口监控 / Serial monitoring**: 监控串口输出查看错误信息 / Monitor serial output for errors
- **连接牢固 / Secure connections**: 验证所有连接牢固 / Verify all connections are secure
- **内存泄漏 / Memory leaks**: 检查堆内存使用情况 / Check heap memory usage

#### 5. USB串口问题 / USB Serial Issues
- **驱动程序 / Drivers**: 安装ESP32-C3 USB驱动 / Install ESP32-C3 USB drivers
- **端口识别 / Port recognition**: 检查设备管理器中的端口 / Check port in device manager
- **线缆质量 / Cable quality**: 使用高质量USB-C数据线 / Use high-quality USB-C data cable
- **重置设备 / Reset device**: 按住BOOT按钮重置 / Hold BOOT button to reset

### 调试模式 / Debug Mode

在 `src/Config.h` 中启用详细调试 / Enable detailed debugging in `src/Config.h`:

```cpp
// 启用串口调试 / Enable serial debugging
#define ENABLE_SERIAL_DEBUG 1

// 设置调试级别 / Set debug level
#define DEBUG_LEVEL 3  // 0=关闭, 1=错误, 2=警告, 3=信息 / 0=off, 1=error, 2=warn, 3=info
```

### 系统数据重置 / Reset System Data

清除所有存储数据（用于测试）/ Clear all stored data (useful for testing):

```cpp
// 在setup()中临时添加 / Add temporarily in setup():
preferences.begin("fishtank", false);
preferences.clear();
preferences.end();
```

### 串口监控命令 / Serial Monitor Commands

```bash
# PlatformIO串口监控 / PlatformIO serial monitor
pio device monitor --baud 115200

# 显示详细信息 / Show verbose info
pio device monitor --baud 115200 --filter esp32_exception_decoder

# Windows串口工具 / Windows serial tools
# PuTTY: 设置波特率115200 / Set baud rate 115200
# Tera Term: 选择正确的COM端口 / Select correct COM port
```

## 🏛️ 系统架构 / System Architecture

系统由以下模块化组件组成 / The system consists of several modular components:

- **SensorDataManager**: 处理传感器数据存储和阈值检查 / Handles sensor data storage and threshold checking
- **PowerManager**: 管理深度睡眠和唤醒周期 / Manages deep sleep and wake-up cycles
- **ConnectivityManager**: 处理WiFi和MQTT连接 / Handles WiFi and MQTT connections
- **ReportingLogic**: 决定何时以及上报什么数据 / Determines when and what to report
- **Config.h**: 集中配置管理 / Centralized configuration management

## 📚 详细文档 / Detailed Documentation

| 文档 / Document | 描述 / Description |
|----------------|-------------------|
| [📦 安装部署指南 / Installation Guide](docs/INSTALLATION_GUIDE.md) | 详细的安装和部署步骤 / Detailed installation and deployment steps |
| [🔬 技术指南 / Technical Guide](docs/TECHNICAL_GUIDE.md) | 深入的技术细节和架构说明 / In-depth technical details and architecture |
| [📡 API参考 / API Reference](docs/API_REFERENCE.md) | MQTT API和数据格式规范 / MQTT API and data format specifications |

## 🤝 贡献 / Contributing

欢迎贡献！请随时提交拉取请求或为错误和功能请求开启问题。

Contributions are welcome! Please feel free to submit pull requests or open issues for bugs and feature requests.

### 开发指南 / Development Guidelines
1. 遵循现有代码风格 / Follow existing code style
2. 添加适当的注释 / Add appropriate comments
3. 更新相关文档 / Update relevant documentation
4. 测试您的更改 / Test your changes

## 📄 许可证 / License

本项目采用MIT许可证开源。请根据您的需要自由修改和分发。

This project is open source under the MIT License. Feel free to modify and distribute according to your needs.

## 🆘 支持 / Support

如果您遇到问题或需要帮助 / If you encounter issues or need help:

1. **查看文档 / Check Documentation**: 首先查看详细文档 / First check the detailed documentation
2. **搜索问题 / Search Issues**: 在GitHub Issues中搜索类似问题 / Search for similar issues in GitHub Issues
3. **创建新问题 / Create New Issue**: 如果找不到解决方案，创建新的问题 / If no solution found, create a new issue
4. **提供详细信息 / Provide Details**: 包括错误日志、配置和硬件信息 / Include error logs, configuration, and hardware info

## 🏷️ 版本历史 / Version History

- **v1.0.0**: 初始版本，支持ESP32-C3和USB串口调试 / Initial release with ESP32-C3 support and USB serial debugging
- **v0.9.0**: ESP32-S3版本 / ESP32-S3 version
- **v0.8.0**: 基础功能实现 / Basic functionality implementation

---

**🐟 让我们一起打造更智能的鱼缸监控系统！/ Let's build a smarter fish tank monitoring system together! 🐟**
