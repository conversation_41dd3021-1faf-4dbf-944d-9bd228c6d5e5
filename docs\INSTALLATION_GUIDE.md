# 📦 安装部署指南 / Installation & Deployment Guide

## 🛠️ 开发环境搭建 / Development Environment Setup

### 1. 安装PlatformIO / Install PlatformIO

#### 方法一：VS Code扩展 / Method 1: VS Code Extension
1. 安装 [Visual Studio Code](https://code.visualstudio.com/)
2. 安装PlatformIO IDE扩展 / Install PlatformIO IDE extension
3. 重启VS Code / Restart VS Code

#### 方法二：命令行安装 / Method 2: Command Line Installation
```bash
# 安装Python (如果未安装) / Install Python (if not installed)
# Windows: 从python.org下载 / Download from python.org
# macOS: brew install python
# Ubuntu: sudo apt install python3 python3-pip

# 安装PlatformIO Core / Install PlatformIO Core
pip install platformio

# 验证安装 / Verify installation
pio --version
```

### 2. ESP32-C3驱动安装 / ESP32-C3 Driver Installation

#### Windows系统 / Windows System
1. 下载ESP32-C3 USB驱动 / Download ESP32-C3 USB driver
2. 连接ESP32-C3到电脑 / Connect ESP32-C3 to computer
3. 安装驱动程序 / Install driver
4. 检查设备管理器中的COM端口 / Check COM port in Device Manager

#### macOS系统 / macOS System
```bash
# 安装驱动 (通常自动识别) / Install driver (usually auto-detected)
# 如果需要手动安装 / If manual installation needed:
brew install --cask silicon-labs-vcp-driver
```

#### Linux系统 / Linux System
```bash
# 添加用户到dialout组 / Add user to dialout group
sudo usermod -a -G dialout $USER

# 重新登录或重启 / Re-login or restart
# 检查设备 / Check device
ls /dev/ttyUSB* /dev/ttyACM*
```

## 📥 项目下载和配置 / Project Download & Configuration

### 1. 克隆项目 / Clone Project
```bash
# 克隆仓库 / Clone repository
git clone <repository-url>
cd fish-tank-monitoring

# 或下载ZIP文件并解压 / Or download ZIP file and extract
```

### 2. 项目结构检查 / Project Structure Check
```
fish-tank-monitoring/
├── src/                    # 源代码 / Source code
├── lib/                    # 库文件 / Libraries
├── platformio.ini          # 项目配置 / Project config
├── README.md              # 项目说明 / Project documentation
└── docs/                  # 详细文档 / Detailed documentation
```

### 3. 配置文件设置 / Configuration File Setup

编辑 `src/Config.h` / Edit `src/Config.h`:

```cpp
// ========== 网络配置 / Network Configuration ==========
#define WIFI_SSID "Your_WiFi_Name"        // 替换为您的WiFi名称 / Replace with your WiFi name
#define WIFI_PASSWORD "Your_WiFi_Password" // 替换为您的WiFi密码 / Replace with your WiFi password

// ========== MQTT配置 / MQTT Configuration ==========
#define MQTT_SERVER "your-mqtt-broker.com"  // MQTT代理服务器 / MQTT broker server
#define MQTT_PORT 1883                      // MQTT端口 / MQTT port
#define MQTT_USER "your_username"           // MQTT用户名 / MQTT username (可选 / optional)
#define MQTT_PASSWORD "your_password"       // MQTT密码 / MQTT password (可选 / optional)

// ========== 设备配置 / Device Configuration ==========
#define DEVICE_ID "fishtank_001"           // 设备唯一标识 / Device unique identifier

// ========== 传感器配置 / Sensor Configuration ==========
#define TDS_THRESHOLD_PPM 50               // TDS变化阈值 / TDS change threshold
#define TEMP_THRESHOLD_C 2.0               // 温度变化阈值 / Temperature change threshold

// ========== 调试配置 / Debug Configuration ==========
#define ENABLE_SERIAL_DEBUG 1              // 启用串口调试 / Enable serial debugging
```

## 🔌 硬件连接 / Hardware Connection

### ESP32-C3引脚连接 / ESP32-C3 Pin Connections

```
ESP32-C3 DevKit    <-->    TDS传感器 / TDS Sensor
================          ===================
GPIO4              <-->    TX (传感器发送 / Sensor transmit)
GPIO5              <-->    RX (传感器接收 / Sensor receive)  
3.3V               <-->    VCC (电源正极 / Power positive)
GND                <-->    GND (电源负极 / Power negative)
```

### 连接步骤 / Connection Steps

1. **断电操作 / Power Off**: 确保ESP32-C3断电 / Ensure ESP32-C3 is powered off
2. **连接信号线 / Connect Signal Lines**: 
   - ESP32-C3 GPIO4 → TDS传感器 TX
   - ESP32-C3 GPIO5 → TDS传感器 RX
3. **连接电源线 / Connect Power Lines**:
   - ESP32-C3 3.3V → TDS传感器 VCC
   - ESP32-C3 GND → TDS传感器 GND
4. **检查连接 / Check Connections**: 确保所有连接牢固 / Ensure all connections are secure
5. **上电测试 / Power On Test**: 连接USB-C线缆上电 / Connect USB-C cable for power

### 连接图示 / Connection Diagram

```
    ESP32-C3 DevKit
    ┌─────────────────┐
    │                 │
    │  GPIO4    GPIO5 │ ←── 连接到TDS传感器 / Connect to TDS sensor
    │                 │
    │  3.3V      GND  │ ←── 电源连接 / Power connections
    │                 │
    │     USB-C       │ ←── 连接到电脑 / Connect to computer
    └─────────────────┘
```

## 🚀 编译和上传 / Build & Upload

### 1. 编译项目 / Build Project

```bash
# 进入项目目录 / Enter project directory
cd fish-tank-monitoring

# 编译项目 / Build project
platformio run

# 或使用VS Code / Or use VS Code
# Ctrl+Shift+P → PlatformIO: Build
```

### 2. 上传固件 / Upload Firmware

```bash
# 上传到设备 / Upload to device
platformio run --target upload

# 或指定端口 / Or specify port
platformio run --target upload --upload-port COM3  # Windows
platformio run --target upload --upload-port /dev/ttyUSB0  # Linux
```

### 3. 监控串口输出 / Monitor Serial Output

```bash
# 监控串口 / Monitor serial
platformio device monitor

# 指定波特率 / Specify baud rate
platformio device monitor --baud 115200

# 退出监控 / Exit monitor: Ctrl+C
```

## 🔧 首次运行配置 / First Run Configuration

### 1. 设备启动检查 / Device Startup Check

连接串口监控，您应该看到类似输出 / Connect serial monitor, you should see output like:

```
=== 鱼缸监控系统启动 / Fish Tank Monitor Starting ===
固件版本 / Firmware Version: 1.0.0
设备ID / Device ID: fishtank_001
自由堆内存 / Free Heap: 180000 bytes

=== WiFi连接 / WiFi Connection ===
正在连接WiFi / Connecting to WiFi: Your_WiFi_Name
WiFi连接成功 / WiFi Connected
IP地址 / IP Address: *************
信号强度 / Signal Strength: -45 dBm

=== MQTT连接 / MQTT Connection ===
正在连接MQTT代理 / Connecting to MQTT broker: your-mqtt-broker.com
MQTT连接成功 / MQTT Connected
订阅主题 / Subscribed to topics

=== 传感器初始化 / Sensor Initialization ===
TDS传感器初始化成功 / TDS sensor initialized successfully
开始数据采集 / Starting data collection...
```

### 2. WiFi配网 (可选) / WiFi Provisioning (Optional)

如果未配置WiFi或连接失败，系统会启动BluFi配网 / If WiFi is not configured or connection fails, system will start BluFi provisioning:

1. **手机安装ESP BluFi应用 / Install ESP BluFi app on phone**
2. **搜索设备 / Search for device**: "FishTank_XXXXXX"
3. **连接并配置WiFi / Connect and configure WiFi**
4. **等待设备重启 / Wait for device restart**

### 3. MQTT测试 / MQTT Testing

使用MQTT客户端工具测试 / Test using MQTT client tools:

```bash
# 使用mosquitto客户端订阅 / Subscribe using mosquitto client
mosquitto_sub -h your-mqtt-broker.com -t "fishtank/+/+"

# 您应该看到数据 / You should see data like:
# fishtank/fishtank_001/sensor {"device_id":"fishtank_001","tds_ppm":245,"temperature_c":24.5}
# fishtank/fishtank_001/status {"device_id":"fishtank_001","status":"online"}
```

## 📊 验证部署 / Deployment Verification

### 1. 功能测试清单 / Function Test Checklist

- [ ] **电源测试 / Power Test**: 设备正常启动 / Device starts normally
- [ ] **WiFi连接 / WiFi Connection**: 成功连接到网络 / Successfully connects to network
- [ ] **MQTT连接 / MQTT Connection**: 成功连接到代理 / Successfully connects to broker
- [ ] **传感器读取 / Sensor Reading**: 能够读取TDS和温度 / Can read TDS and temperature
- [ ] **数据上报 / Data Reporting**: 数据正常上报到MQTT / Data reports to MQTT normally
- [ ] **串口调试 / Serial Debug**: 调试信息正常输出 / Debug info outputs normally
- [ ] **深度睡眠 / Deep Sleep**: 设备能够进入和唤醒 / Device can enter and wake from sleep

### 2. 性能验证 / Performance Verification

```bash
# 检查内存使用 / Check memory usage
# 在串口监控中查看 / Check in serial monitor:
# "自由堆内存 / Free Heap: XXXXX bytes" (应该 > 100KB / should be > 100KB)

# 检查WiFi信号 / Check WiFi signal
# "WiFi信号强度 / WiFi RSSI: -XX dBm" (应该 > -70dBm / should be > -70dBm)

# 检查传感器响应 / Check sensor response
# "传感器读取成功 / Sensor read success" (应该无错误 / should be no errors)
```

### 3. 长期运行测试 / Long-term Running Test

1. **24小时测试 / 24-hour test**: 观察系统稳定性 / Observe system stability
2. **数据一致性 / Data consistency**: 检查数据上报是否正常 / Check if data reporting is normal
3. **内存泄漏检查 / Memory leak check**: 监控堆内存变化 / Monitor heap memory changes
4. **网络重连测试 / Network reconnection test**: 断开网络后重连 / Disconnect and reconnect network

## 🔄 维护和更新 / Maintenance & Updates

### 固件更新 / Firmware Update
```bash
# 拉取最新代码 / Pull latest code
git pull origin main

# 重新编译上传 / Rebuild and upload
platformio run --target upload
```

### 配置备份 / Configuration Backup
```bash
# 备份配置文件 / Backup configuration
cp src/Config.h src/Config.h.backup
```

### 日志收集 / Log Collection
```bash
# 收集串口日志 / Collect serial logs
platformio device monitor > system.log
```
