# 📡 API参考文档 / API Reference

## MQTT API接口 / MQTT API Interface

### 主题结构 / Topic Structure

所有MQTT主题遵循以下格式 / All MQTT topics follow this format:
```
fishtank/{device_id}/{message_type}
```

- `device_id`: 设备唯一标识符 / Device unique identifier
- `message_type`: 消息类型 / Message type

### 消息类型 / Message Types

| 消息类型 / Type | 主题 / Topic | 方向 / Direction | 描述 / Description |
|----------------|-------------|-----------------|-------------------|
| `sensor` | `fishtank/{device_id}/sensor` | 设备→服务器 / Device→Server | 传感器数据 / Sensor data |
| `status` | `fishtank/{device_id}/status` | 设备→服务器 / Device→Server | 设备状态 / Device status |
| `error` | `fishtank/{device_id}/error` | 设备→服务器 / Device→Server | 错误报告 / Error reports |
| `heartbeat` | `fishtank/{device_id}/heartbeat` | 设备→服务器 / Device→Server | 心跳消息 / Heartbeat |
| `command` | `fishtank/{device_id}/command` | 服务器→设备 / Server→Device | 远程命令 / Remote commands |
| `config` | `fishtank/{device_id}/config` | 服务器→设备 / Server→Device | 配置更新 / Configuration updates |

## 📊 数据格式规范 / Data Format Specification

### 1. 传感器数据 / Sensor Data

**主题 / Topic**: `fishtank/{device_id}/sensor`

**数据结构 / Data Structure**:
```json
{
  "device_id": "string",           // 设备ID / Device ID
  "timestamp": "number",           // Unix时间戳 / Unix timestamp
  "tds_ppm": "number",            // TDS值(ppm) / TDS value in ppm
  "temperature_c": "number",       // 温度(°C) / Temperature in Celsius
  "battery_voltage": "number",     // 电池电压(V) / Battery voltage in volts
  "wifi_rssi": "number",          // WiFi信号强度(dBm) / WiFi signal strength
  "report_reason": "string",       // 上报原因 / Report reason
  "uptime_seconds": "number",      // 运行时间(秒) / Uptime in seconds
  "free_heap": "number",          // 可用堆内存(字节) / Free heap memory in bytes
  "boot_count": "number"          // 启动次数 / Boot count
}
```

**示例 / Example**:
```json
{
  "device_id": "fishtank_001",
  "timestamp": 1640995200,
  "tds_ppm": 245.5,
  "temperature_c": 24.3,
  "battery_voltage": 3.8,
  "wifi_rssi": -45,
  "report_reason": "tds_threshold",
  "uptime_seconds": 3600,
  "free_heap": 180000,
  "boot_count": 15
}
```

**上报原因 / Report Reasons**:
- `"tds_threshold"`: TDS变化超过阈值 / TDS change exceeds threshold
- `"temperature_threshold"`: 温度变化超过阈值 / Temperature change exceeds threshold
- `"periodic"`: 定时上报 / Periodic report
- `"startup"`: 系统启动 / System startup
- `"manual"`: 手动触发 / Manual trigger

### 2. 设备状态 / Device Status

**主题 / Topic**: `fishtank/{device_id}/status`

**数据结构 / Data Structure**:
```json
{
  "device_id": "string",           // 设备ID / Device ID
  "timestamp": "number",           // Unix时间戳 / Unix timestamp
  "status": "string",             // 设备状态 / Device status
  "firmware_version": "string",    // 固件版本 / Firmware version
  "wifi_rssi": "number",          // WiFi信号强度 / WiFi signal strength
  "free_heap": "number",          // 可用堆内存 / Free heap memory
  "boot_count": "number",         // 启动次数 / Boot count
  "uptime_seconds": "number",     // 运行时间 / Uptime
  "last_sensor_read": "number"    // 上次传感器读取时间 / Last sensor read time
}
```

**设备状态值 / Device Status Values**:
- `"online"`: 设备在线 / Device online
- `"offline"`: 设备离线 / Device offline
- `"sleeping"`: 设备睡眠 / Device sleeping
- `"error"`: 设备错误 / Device error
- `"updating"`: 固件更新中 / Firmware updating

### 3. 错误报告 / Error Reports

**主题 / Topic**: `fishtank/{device_id}/error`

**数据结构 / Data Structure**:
```json
{
  "device_id": "string",           // 设备ID / Device ID
  "timestamp": "number",           // Unix时间戳 / Unix timestamp
  "error_type": "string",         // 错误类型 / Error type
  "error_code": "number",         // 错误代码 / Error code
  "error_message": "string",      // 错误消息 / Error message
  "battery_voltage": "number",    // 电池电压 / Battery voltage
  "wifi_rssi": "number",         // WiFi信号强度 / WiFi signal strength
  "free_heap": "number",         // 可用堆内存 / Free heap memory
  "context": "object"            // 错误上下文 / Error context
}
```

**错误类型 / Error Types**:
- `"sensor_timeout"`: 传感器超时 / Sensor timeout
- `"sensor_crc_error"`: CRC校验错误 / CRC check error
- `"wifi_connection_failed"`: WiFi连接失败 / WiFi connection failed
- `"mqtt_connection_failed"`: MQTT连接失败 / MQTT connection failed
- `"low_battery"`: 电池电量低 / Low battery
- `"memory_error"`: 内存错误 / Memory error
- `"system_error"`: 系统错误 / System error

### 4. 心跳消息 / Heartbeat

**主题 / Topic**: `fishtank/{device_id}/heartbeat`

**数据结构 / Data Structure**:
```json
{
  "device_id": "string",           // 设备ID / Device ID
  "timestamp": "number",           // Unix时间戳 / Unix timestamp
  "status": "string",             // 设备状态 / Device status
  "uptime_seconds": "number",     // 运行时间 / Uptime
  "free_heap": "number",          // 可用堆内存 / Free heap memory
  "wifi_rssi": "number"          // WiFi信号强度 / WiFi signal strength
}
```

## 🎛️ 远程控制API / Remote Control API

### 1. 远程命令 / Remote Commands

**主题 / Topic**: `fishtank/{device_id}/command`

**命令格式 / Command Format**:
```json
{
  "command": "string",            // 命令类型 / Command type
  "parameters": "object",         // 命令参数 / Command parameters
  "timestamp": "number",          // 时间戳 / Timestamp
  "request_id": "string"         // 请求ID / Request ID
}
```

**支持的命令 / Supported Commands**:

#### 立即读取传感器 / Immediate Sensor Reading
```json
{
  "command": "read_sensor",
  "parameters": {},
  "timestamp": 1640995200,
  "request_id": "req_001"
}
```

#### 设置上报间隔 / Set Report Interval
```json
{
  "command": "set_report_interval",
  "parameters": {
    "interval_seconds": 1800
  },
  "timestamp": 1640995200,
  "request_id": "req_002"
}
```

#### 设置阈值 / Set Thresholds
```json
{
  "command": "set_thresholds",
  "parameters": {
    "tds_threshold_ppm": 100,
    "temperature_threshold_c": 1.5
  },
  "timestamp": 1640995200,
  "request_id": "req_003"
}
```

#### 重启设备 / Restart Device
```json
{
  "command": "restart",
  "parameters": {
    "delay_seconds": 5
  },
  "timestamp": 1640995200,
  "request_id": "req_004"
}
```

#### 进入睡眠模式 / Enter Sleep Mode
```json
{
  "command": "sleep",
  "parameters": {
    "duration_seconds": 3600
  },
  "timestamp": 1640995200,
  "request_id": "req_005"
}
```

### 2. 配置更新 / Configuration Updates

**主题 / Topic**: `fishtank/{device_id}/config`

**配置格式 / Configuration Format**:
```json
{
  "config_type": "string",        // 配置类型 / Configuration type
  "config_data": "object",        // 配置数据 / Configuration data
  "timestamp": "number",          // 时间戳 / Timestamp
  "version": "string"            // 配置版本 / Configuration version
}
```

#### WiFi配置 / WiFi Configuration
```json
{
  "config_type": "wifi",
  "config_data": {
    "ssid": "new_wifi_name",
    "password": "new_wifi_password"
  },
  "timestamp": 1640995200,
  "version": "1.0"
}
```

#### MQTT配置 / MQTT Configuration
```json
{
  "config_type": "mqtt",
  "config_data": {
    "server": "new-broker.com",
    "port": 1883,
    "username": "new_user",
    "password": "new_pass"
  },
  "timestamp": 1640995200,
  "version": "1.0"
}
```

## 📈 数据查询API / Data Query API

### 历史数据查询 / Historical Data Query

虽然设备本身不存储历史数据，但可以通过MQTT代理或后端服务查询 / While the device doesn't store historical data, it can be queried through MQTT broker or backend service:

**查询请求 / Query Request**:
```json
{
  "query_type": "historical_data",
  "device_id": "fishtank_001",
  "start_time": 1640995200,
  "end_time": 1641081600,
  "data_types": ["sensor", "status"],
  "limit": 1000
}
```

**查询响应 / Query Response**:
```json
{
  "query_id": "query_001",
  "device_id": "fishtank_001",
  "total_records": 150,
  "data": [
    {
      "timestamp": 1640995200,
      "type": "sensor",
      "data": { /* 传感器数据 / sensor data */ }
    }
  ]
}
```

## 🔐 认证和安全 / Authentication & Security

### MQTT认证 / MQTT Authentication

设备支持以下认证方式 / Device supports the following authentication methods:

1. **无认证 / No Authentication**: 用于测试环境 / For testing environments
2. **用户名密码 / Username/Password**: 基本认证 / Basic authentication
3. **TLS/SSL**: 加密连接 / Encrypted connection (计划中 / planned)
4. **证书认证 / Certificate Authentication**: 双向认证 / Mutual authentication (计划中 / planned)

### 数据加密 / Data Encryption

- **传输加密 / Transport Encryption**: 支持TLS 1.2+ / Supports TLS 1.2+
- **消息签名 / Message Signing**: HMAC-SHA256 (可选 / optional)
- **敏感数据保护 / Sensitive Data Protection**: 密码和密钥不明文传输 / Passwords and keys not transmitted in plain text

## 📝 错误代码参考 / Error Code Reference

| 错误代码 / Code | 错误类型 / Type | 描述 / Description |
|----------------|----------------|-------------------|
| 1001 | SENSOR_TIMEOUT | 传感器响应超时 / Sensor response timeout |
| 1002 | SENSOR_CRC_ERROR | 传感器数据CRC错误 / Sensor data CRC error |
| 1003 | SENSOR_INVALID_DATA | 传感器数据无效 / Invalid sensor data |
| 2001 | WIFI_CONNECT_FAILED | WiFi连接失败 / WiFi connection failed |
| 2002 | WIFI_TIMEOUT | WiFi连接超时 / WiFi connection timeout |
| 2003 | WIFI_AUTH_FAILED | WiFi认证失败 / WiFi authentication failed |
| 3001 | MQTT_CONNECT_FAILED | MQTT连接失败 / MQTT connection failed |
| 3002 | MQTT_PUBLISH_FAILED | MQTT发布失败 / MQTT publish failed |
| 3003 | MQTT_SUBSCRIBE_FAILED | MQTT订阅失败 / MQTT subscribe failed |
| 4001 | LOW_BATTERY | 电池电量低 / Low battery |
| 4002 | MEMORY_FULL | 内存不足 / Memory full |
| 4003 | NVS_ERROR | NVS存储错误 / NVS storage error |
| 5001 | SYSTEM_ERROR | 系统错误 / System error |
| 5002 | WATCHDOG_TIMEOUT | 看门狗超时 / Watchdog timeout |
| 5003 | STACK_OVERFLOW | 栈溢出 / Stack overflow |
