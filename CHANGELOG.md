# 📝 更新日志 / Changelog

本文档记录了鱼缸监控系统的所有重要更改。

All notable changes to the Fish Tank Monitoring System will be documented in this file.

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0] - 2024-01-XX

### 🎉 新增 / Added
- **ESP32-C3支持**: 完整支持ESP32-C3开发板 / Full ESP32-C3 development board support
- **USB串口调试**: 原生USB-C串口调试功能 / Native USB-C serial debugging
- **中英双语文档**: 完整的中英文双语文档系统 / Complete bilingual documentation system
- **模块化架构**: 清晰的模块化代码架构 / Clean modular code architecture
- **智能上报逻辑**: 基于阈值和时间的智能数据上报 / Intelligent data reporting based on thresholds and time
- **电源管理**: 深度睡眠和电池电量监控 / Deep sleep and battery monitoring
- **错误处理**: 完善的错误处理和恢复机制 / Comprehensive error handling and recovery
- **配置管理**: 集中化配置管理系统 / Centralized configuration management
- **MQTT通信**: 稳定的MQTT数据传输 / Stable MQTT data transmission
- **NVS存储**: 非易失性存储数据持久化 / Non-volatile storage data persistence

### 🔧 技术特性 / Technical Features
- **硬件平台**: ESP32-C3 RISC-V 160MHz / ESP32-C3 RISC-V 160MHz
- **内存优化**: RAM使用率 < 20% / RAM usage < 20%
- **Flash优化**: Flash使用率 < 60% / Flash usage < 60%
- **功耗优化**: 深度睡眠电流 ~10µA / Deep sleep current ~10µA
- **网络支持**: WiFi 802.11 b/g/n (2.4GHz) / WiFi 802.11 b/g/n (2.4GHz)
- **传感器接口**: UART TDS传感器支持 / UART TDS sensor support
- **调试接口**: USB-C原生串口 / USB-C native serial

### 📚 文档 / Documentation
- **README.md**: 完整的项目介绍和快速开始指南 / Complete project introduction and quick start guide
- **INSTALLATION_GUIDE.md**: 详细的安装部署指南 / Detailed installation and deployment guide
- **TECHNICAL_GUIDE.md**: 深入的技术指南和架构说明 / In-depth technical guide and architecture
- **API_REFERENCE.md**: MQTT API和数据格式规范 / MQTT API and data format specifications
- **CHANGELOG.md**: 版本更新日志 / Version changelog

### 🛠️ 开发工具 / Development Tools
- **PlatformIO**: 现代化的嵌入式开发环境 / Modern embedded development environment
- **VS Code**: 推荐的开发IDE / Recommended development IDE
- **Git**: 版本控制系统 / Version control system
- **Markdown**: 文档格式 / Documentation format

## [0.9.0] - 2024-01-XX

### 🔄 更改 / Changed
- **硬件平台迁移**: 从ESP32-S3迁移到ESP32-C3 / Migrated from ESP32-S3 to ESP32-C3
- **串口调试**: 从硬件串口改为USB串口 / Changed from hardware serial to USB serial
- **引脚配置**: 更新GPIO引脚配置适配ESP32-C3 / Updated GPIO pin configuration for ESP32-C3
- **编译配置**: 优化PlatformIO配置 / Optimized PlatformIO configuration

### 🐛 修复 / Fixed
- **编译错误**: 修复ESP32-C3编译问题 / Fixed ESP32-C3 compilation issues
- **Serial未定义**: 解决USB CDC配置问题 / Resolved USB CDC configuration issues
- **内存优化**: 修复内存使用过高问题 / Fixed high memory usage issues
- **分区配置**: 使用huge_app分区解决Flash空间不足 / Used huge_app partition to solve Flash space issues

### ⚠️ 已知问题 / Known Issues
- BluFi配网功能需要进一步测试 / BluFi provisioning needs further testing
- 长期运行稳定性需要验证 / Long-term running stability needs verification

## [0.8.0] - 2024-01-XX

### 🎉 新增 / Added
- **基础功能**: 实现TDS和温度传感器读取 / Basic TDS and temperature sensor reading
- **WiFi连接**: 基本WiFi连接功能 / Basic WiFi connection functionality
- **MQTT通信**: 基本MQTT数据传输 / Basic MQTT data transmission
- **数据存储**: NVS数据持久化 / NVS data persistence
- **配置系统**: 基本配置管理 / Basic configuration management

### 🏗️ 架构 / Architecture
- **SensorDataManager**: 传感器数据管理模块 / Sensor data management module
- **ConnectivityManager**: 网络连接管理模块 / Network connectivity management module
- **PowerManager**: 电源管理模块 / Power management module
- **ReportingLogic**: 数据上报逻辑模块 / Data reporting logic module

## [0.7.0] - 2024-01-XX

### 🎯 计划 / Planned
- **项目初始化**: 项目结构设计和初始化 / Project structure design and initialization
- **需求分析**: 功能需求分析和设计 / Functional requirements analysis and design
- **技术选型**: 硬件和软件技术栈选择 / Hardware and software technology stack selection

---

## 🔮 未来计划 / Future Plans

### v1.1.0 (计划中 / Planned)
- [ ] **Web界面**: 基于Web的配置和监控界面 / Web-based configuration and monitoring interface
- [ ] **移动应用**: 手机APP支持 / Mobile app support
- [ ] **数据分析**: 历史数据分析和趋势预测 / Historical data analysis and trend prediction
- [ ] **告警系统**: 智能告警和通知系统 / Intelligent alarm and notification system
- [ ] **多传感器**: 支持更多类型的传感器 / Support for more sensor types
- [ ] **云服务**: 云端数据存储和分析 / Cloud data storage and analysis

### v1.2.0 (计划中 / Planned)
- [ ] **OTA更新**: 无线固件更新功能 / Over-the-air firmware update
- [ ] **安全增强**: TLS/SSL加密和证书认证 / TLS/SSL encryption and certificate authentication
- [ ] **多设备管理**: 支持多个设备集中管理 / Multi-device centralized management
- [ ] **API扩展**: RESTful API支持 / RESTful API support
- [ ] **数据导出**: 数据导出和备份功能 / Data export and backup functionality

### v2.0.0 (远期计划 / Long-term Plan)
- [ ] **AI集成**: 机器学习和AI预测功能 / Machine learning and AI prediction
- [ ] **边缘计算**: 本地数据处理和分析 / Local data processing and analysis
- [ ] **物联网平台**: 完整的IoT平台解决方案 / Complete IoT platform solution
- [ ] **商业化**: 产品化和商业化支持 / Productization and commercialization support

---

## 📋 版本说明 / Version Notes

### 版本号规则 / Version Numbering Rules
- **主版本号 / Major**: 不兼容的API更改 / Incompatible API changes
- **次版本号 / Minor**: 向后兼容的功能性新增 / Backward compatible functionality additions
- **修订号 / Patch**: 向后兼容的问题修正 / Backward compatible bug fixes

### 发布周期 / Release Cycle
- **主版本 / Major**: 每年1-2次 / 1-2 times per year
- **次版本 / Minor**: 每季度1次 / Once per quarter
- **修订版本 / Patch**: 根据需要 / As needed

### 支持政策 / Support Policy
- **当前版本**: 完全支持 / Full support
- **前一版本**: 安全更新 / Security updates only
- **更早版本**: 不再支持 / No longer supported

---

**📅 最后更新 / Last Updated**: 2024-01-XX
**👥 维护者 / Maintainer**: Fish Tank Monitoring Team
